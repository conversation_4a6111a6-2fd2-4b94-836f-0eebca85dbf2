import React, { useState, useEffect, useCallback, useMemo, useRef, useLayoutEffect } from 'react';
import './CardCarousel.css';

interface Card {
  id: number;
  title: string;
  content: string;
  image: string;
  color: string;
}

interface CardCarouselProps {
  cards: Card[];
  autoPlayInterval?: number;
  pauseOnHover?: boolean;
}

const CardCarousel: React.FC<CardCarouselProps> = ({
  cards,
  autoPlayInterval = 3000,
  pauseOnHover = true
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const [isPaused, setIsPaused] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const carouselRef = useRef<HTMLDivElement>(null);

  // 使用 useMemo 优化卡片数据，创建无限循环的卡片数组
  const infiniteCards = useMemo(() => {
    if (cards.length === 0) return [];
    // 在前后各添加一张卡片以实现无限循环效果
    return [cards[cards.length - 1], ...cards, cards[0]];
  }, [cards]);

  // 使用 useCallback 优化函数，避免不必要的重新渲染
  const goToNext = useCallback(() => {
    setCurrentIndex(prevIndex => {
      const nextIndex = prevIndex + 1;
      // 如果到达最后一张（实际是第一张的副本），重置到真正的第一张
      if (nextIndex >= infiniteCards.length - 1) {
        setTimeout(() => setCurrentIndex(1), 300); // 延迟重置，让过渡动画完成
        return nextIndex;
      }
      return nextIndex;
    });
  }, [infiniteCards.length]);

  const goToPrev = useCallback(() => {
    setCurrentIndex(prevIndex => {
      const nextIndex = prevIndex - 1;
      // 如果到达第一张（实际是最后一张的副本），重置到真正的最后一张
      if (nextIndex <= 0) {
        setTimeout(() => setCurrentIndex(infiniteCards.length - 2), 300);
        return nextIndex;
      }
      return nextIndex;
    });
  }, [infiniteCards.length]);

  // 自动播放逻辑
  const startAutoPlay = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    intervalRef.current = setInterval(goToNext, autoPlayInterval);
  }, [goToNext, autoPlayInterval]);

  const stopAutoPlay = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  // 鼠标悬停处理
  const handleMouseEnter = useCallback(() => {
    if (pauseOnHover) {
      setIsPaused(true);
      stopAutoPlay();
    }
  }, [pauseOnHover, stopAutoPlay]);

  const handleMouseLeave = useCallback(() => {
    if (pauseOnHover) {
      setIsPaused(false);
      if (isPlaying) {
        startAutoPlay();
      }
    }
  }, [pauseOnHover, isPlaying, startAutoPlay]);

  // 播放/暂停切换
  const togglePlayPause = useCallback(() => {
    setIsPlaying(prev => {
      const newState = !prev;
      if (newState && !isPaused) {
        startAutoPlay();
      } else {
        stopAutoPlay();
      }
      return newState;
    });
  }, [isPaused, startAutoPlay, stopAutoPlay]);

  // 使用 useEffect 管理自动播放
  useEffect(() => {
    if (isPlaying && !isPaused) {
      startAutoPlay();
    } else {
      stopAutoPlay();
    }

    return () => stopAutoPlay();
  }, [isPlaying, isPaused, startAutoPlay, stopAutoPlay]);

  // 使用 useLayoutEffect 处理初始位置
  useLayoutEffect(() => {
    if (infiniteCards.length > 0) {
      setCurrentIndex(1); // 从真正的第一张开始
    }
  }, [infiniteCards.length]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  if (cards.length === 0) {
    return <div className="carousel-empty">No cards to display</div>;
  }

  return (
    <div className="card-carousel" ref={carouselRef}>
      <div className="carousel-header">
        <h2>Card Carousel</h2>
        <div className="carousel-controls">
          <button onClick={goToPrev} className="control-btn">
            ←
          </button>
          <button onClick={togglePlayPause} className="control-btn">
            {isPlaying ? '⏸️' : '▶️'}
          </button>
          <button onClick={goToNext} className="control-btn">
            →
          </button>
        </div>
      </div>
      
      <div 
        className="carousel-container"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <div 
          className="carousel-track"
          style={{
            transform: `translateX(-${currentIndex * 100}%)`,
            transition: currentIndex === 0 || currentIndex === infiniteCards.length - 1 
              ? 'none' 
              : 'transform 0.3s ease-in-out'
          }}
        >
          {infiniteCards.map((card, index) => (
            <div 
              key={`${card.id}-${index}`} 
              className="carousel-card"
              style={{ backgroundColor: card.color }}
            >
              <div className="card-image">
                <img src={card.image} alt={card.title} />
              </div>
              <div className="card-content">
                <h3>{card.title}</h3>
                <p>{card.content}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="carousel-indicators">
        {cards.map((_, index) => (
          <button
            key={index}
            className={`indicator ${
              (currentIndex - 1 + cards.length) % cards.length === index ? 'active' : ''
            }`}
            onClick={() => setCurrentIndex(index + 1)}
          />
        ))}
      </div>

      <div className="carousel-status">
        <span className={`status-indicator ${isPlaying && !isPaused ? 'playing' : 'paused'}`}>
          {isPlaying && !isPaused ? 'Playing' : 'Paused'}
        </span>
      </div>
    </div>
  );
};

export default CardCarousel;
