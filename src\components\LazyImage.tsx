import React, { useState, useCallback, useMemo } from 'react';
import { useIntersectionObserver } from '../hooks/useIntersectionObserver';
import './LazyImage.css';

interface LazyImageProps {
  src: string;
  alt: string;
  placeholder?: string;
  className?: string;
  width?: number | string;
  height?: number | string;
  onLoad?: () => void;
  onError?: () => void;
  threshold?: number;
  rootMargin?: string;
  blurDataURL?: string;
}

const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  placeholder,
  className = '',
  width,
  height,
  onLoad,
  onError,
  threshold = 0.1,
  rootMargin = '50px',
  blurDataURL
}) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [imageSrc, setImageSrc] = useState<string | null>(null);

  // 使用自定义的 useIntersectionObserver hook
  const { isIntersecting, ref } = useIntersectionObserver({
    threshold,
    rootMargin,
    freezeOnceVisible: true
  });

  // 当图片进入视口时开始加载
  React.useEffect(() => {
    if (isIntersecting && !imageSrc && !imageError) {
      setImageSrc(src);
    }
  }, [isIntersecting, src, imageSrc, imageError]);

  const handleImageLoad = useCallback(() => {
    setImageLoaded(true);
    onLoad?.();
  }, [onLoad]);

  const handleImageError = useCallback(() => {
    setImageError(true);
    onError?.();
  }, [onError]);

  // 生成默认占位符
  const defaultPlaceholder = useMemo(() => {
    if (blurDataURL) return blurDataURL;
    
    // 生成一个简单的SVG占位符
    const svgPlaceholder = `data:image/svg+xml;base64,${btoa(`
      <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#f0f0f0"/>
        <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#999" font-family="Arial, sans-serif" font-size="16">
          Loading...
        </text>
      </svg>
    `)}`;
    
    return svgPlaceholder;
  }, [blurDataURL]);

  const imageStyle = useMemo(() => ({
    width: width || '100%',
    height: height || 'auto'
  }), [width, height]);

  return (
    <div 
      ref={ref}
      className={`lazy-image-container ${className}`}
      style={imageStyle}
    >
      {/* 占位符 */}
      {!imageLoaded && !imageError && (
        <div className="lazy-image-placeholder">
          <img
            src={placeholder || defaultPlaceholder}
            alt=""
            className="placeholder-image"
            style={imageStyle}
          />
          <div className="loading-overlay">
            <div className="loading-spinner"></div>
          </div>
        </div>
      )}

      {/* 实际图片 */}
      {imageSrc && !imageError && (
        <img
          src={imageSrc}
          alt={alt}
          className={`lazy-image ${imageLoaded ? 'loaded' : 'loading'}`}
          style={imageStyle}
          onLoad={handleImageLoad}
          onError={handleImageError}
        />
      )}

      {/* 错误状态 */}
      {imageError && (
        <div className="lazy-image-error" style={imageStyle}>
          <div className="error-content">
            <span className="error-icon">⚠️</span>
            <p>Failed to load image</p>
            <button 
              className="retry-button"
              onClick={() => {
                setImageError(false);
                setImageSrc(null);
                setImageLoaded(false);
              }}
            >
              Retry
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default LazyImage;
