.lazy-image-container {
  position: relative;
  overflow: hidden;
  background-color: #f5f5f5;
  border-radius: 0.5rem;
}

.lazy-image-placeholder {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: blur(5px);
  transition: filter 0.3s ease;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.lazy-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease, transform 0.3s ease;
  opacity: 0;
  transform: scale(1.05);
}

.lazy-image.loading {
  opacity: 0;
  transform: scale(1.05);
}

.lazy-image.loaded {
  opacity: 1;
  transform: scale(1);
}

.lazy-image-error {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #333;
  min-height: 200px;
}

.error-content {
  text-align: center;
  padding: 1rem;
}

.error-icon {
  font-size: 2rem;
  display: block;
  margin-bottom: 0.5rem;
}

.error-content p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
  color: #666;
}

.retry-button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.25rem;
  background: #667eea;
  color: white;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s ease;
}

.retry-button:hover {
  background: #5a67d8;
}

.retry-button:active {
  transform: translateY(1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-spinner {
    width: 1.5rem;
    height: 1.5rem;
    border-width: 2px;
  }
  
  .error-content {
    padding: 0.75rem;
  }
  
  .error-icon {
    font-size: 1.5rem;
  }
  
  .error-content p {
    font-size: 0.8rem;
  }
  
  .retry-button {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
}

/* 减少动画效果（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .placeholder-image,
  .lazy-image,
  .retry-button {
    transition: none;
  }
  
  .loading-spinner {
    animation: none;
  }
}
