/* 使用 CSS 变量和响应式单位 */
:root {
  --grid-gap: clamp(1rem, 2vw, 2rem);
  --item-border-radius: 1rem;
  --item-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.1);
  --item-hover-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.15);
  --transition: all 0.3s ease;
}

.infinite-scroll {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.infinite-scroll-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 0 0.5rem;
}

.infinite-scroll-header h2 {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  color: #333;
  margin: 0;
  font-weight: 600;
}

.scroll-stats {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.9rem;
  color: #666;
}

.reset-button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: var(--transition);
}

.reset-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--item-hover-shadow);
}

.infinite-scroll-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(min(100%, 300px), 1fr));
  gap: var(--grid-gap);
  margin-bottom: 2rem;
}

.infinite-scroll-item {
  background: white;
  border-radius: var(--item-border-radius);
  box-shadow: var(--item-shadow);
  overflow: hidden;
  transition: var(--transition);
  cursor: pointer;
}

.infinite-scroll-item:hover {
  transform: translateY(-4px);
  box-shadow: var(--item-hover-shadow);
}

.item-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
  position: relative;
}

.item-content {
  padding: 1.5rem;
}

.item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  font-size: 0.8rem;
}

.item-category {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-weight: 500;
}

.item-date {
  color: #999;
}

.item-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.75rem 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-description {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.6;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.infinite-scroll-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.infinite-scroll-loading p {
  color: #666;
  font-size: 1rem;
  margin: 0;
}

.infinite-scroll-empty,
.infinite-scroll-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  background: #f8f9fa;
  border-radius: var(--item-border-radius);
  margin: 2rem 0;
}

.infinite-scroll-empty p,
.infinite-scroll-error p {
  color: #666;
  font-size: 1.1rem;
  margin: 0 0 1.5rem 0;
}

.retry-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: var(--transition);
}

.retry-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--item-hover-shadow);
}

.infinite-scroll-end {
  text-align: center;
  padding: 3rem 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: var(--item-border-radius);
  margin-top: 2rem;
}

.infinite-scroll-end p {
  margin: 0.5rem 0;
  font-size: 1rem;
}

.infinite-scroll-end p:first-child {
  font-size: 1.2rem;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  :root {
    --grid-gap: 1rem;
  }
  
  .infinite-scroll {
    padding: 1rem 0.5rem;
  }
  
  .infinite-scroll-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .scroll-stats {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .infinite-scroll-grid {
    grid-template-columns: 1fr;
  }
  
  .item-content {
    padding: 1rem;
  }
  
  .item-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .loading-spinner {
    width: 2rem;
    height: 2rem;
    border-width: 3px;
  }
}

@media (max-width: 480px) {
  .infinite-scroll {
    padding: 0.5rem;
  }
  
  .item-image {
    height: 150px;
  }
  
  .item-content {
    padding: 0.75rem;
  }
  
  .infinite-scroll-loading,
  .infinite-scroll-empty,
  .infinite-scroll-error {
    padding: 2rem 1rem;
  }
}

/* 高分辨率屏幕优化 */
@media (min-width: 1200px) {
  .infinite-scroll-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

/* 减少动画效果（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .infinite-scroll-item,
  .reset-button,
  .retry-button {
    transition: none;
  }
  
  .infinite-scroll-item:hover {
    transform: none;
  }
  
  .loading-spinner {
    animation: none;
  }
}
