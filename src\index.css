/* 响应式设计的全局样式 */
:root {
  /* 字体系统 */
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
  font-weight: 400;

  /* 响应式字体大小 - 使用 clamp() 实现流体排版 */
  --font-size-xs: clamp(0.75rem, 2vw, 0.875rem);
  --font-size-sm: clamp(0.875rem, 2.5vw, 1rem);
  --font-size-base: clamp(1rem, 3vw, 1.125rem);
  --font-size-lg: clamp(1.125rem, 3.5vw, 1.25rem);
  --font-size-xl: clamp(1.25rem, 4vw, 1.5rem);
  --font-size-2xl: clamp(1.5rem, 5vw, 2rem);
  --font-size-3xl: clamp(2rem, 6vw, 3rem);

  /* 响应式间距 - 使用 vw 和 rem 结合 */
  --spacing-xs: clamp(0.25rem, 1vw, 0.5rem);
  --spacing-sm: clamp(0.5rem, 2vw, 1rem);
  --spacing-md: clamp(1rem, 3vw, 1.5rem);
  --spacing-lg: clamp(1.5rem, 4vw, 2rem);
  --spacing-xl: clamp(2rem, 5vw, 3rem);
  --spacing-2xl: clamp(3rem, 6vw, 4rem);

  /* 容器宽度 */
  --container-xs: 100%;
  --container-sm: min(100%, 640px);
  --container-md: min(100%, 768px);
  --container-lg: min(100%, 1024px);
  --container-xl: min(100%, 1280px);
  --container-2xl: min(100%, 1536px);

  /* 颜色系统 */
  --color-primary: #667eea;
  --color-primary-dark: #5a67d8;
  --color-secondary: #764ba2;
  --color-accent: #f093fb;
  --color-success: #48bb78;
  --color-warning: #ed8936;
  --color-error: #f56565;
  --color-info: #4299e1;

  /* 中性色 */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* 背景和文字颜色 */
  --color-bg: #ffffff;
  --color-bg-secondary: var(--color-gray-50);
  --color-text: var(--color-gray-900);
  --color-text-secondary: var(--color-gray-600);
  --color-text-muted: var(--color-gray-500);

  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* 边框半径 */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;
  --radius-full: 9999px;

  /* 过渡动画 */
  --transition-fast: 150ms ease;
  --transition-base: 250ms ease;
  --transition-slow: 350ms ease;

  /* Z-index 层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;

  color-scheme: light;
  color: var(--color-text);
  background-color: var(--color-bg);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 暗色主题 */
@media (prefers-color-scheme: dark) {
  :root {
    --color-bg: var(--color-gray-900);
    --color-bg-secondary: var(--color-gray-800);
    --color-text: var(--color-gray-50);
    --color-text-secondary: var(--color-gray-300);
    --color-text-muted: var(--color-gray-400);

    color-scheme: dark;
  }
}

/* 重置和基础样式 */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  /* 使用 62.5% 使 1rem = 10px，便于计算 */
  font-size: 62.5%;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--color-text);
  background-color: var(--color-bg);
  min-height: 100vh;
  min-width: 320px;
  overflow-x: hidden;
}

/* 链接样式 */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: var(--transition-fast);
}

a:hover {
  color: var(--color-primary-dark);
  text-decoration: underline;
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
  margin: 0 0 var(--spacing-md) 0;
  font-weight: 600;
  line-height: 1.2;
  color: var(--color-text);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

/* 段落样式 */
p {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--color-text-secondary);
}

/* 按钮基础样式 */
button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  font-family: inherit;
  cursor: pointer;
  transition: var(--transition-base);
  text-decoration: none;
  background-color: var(--color-primary);
  color: white;
}

button:hover {
  background-color: var(--color-primary-dark);
  transform: translateY(-1px);
}

button:active {
  transform: translateY(0);
}

button:focus,
button:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 输入框样式 */
input, textarea, select {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-family: inherit;
  transition: var(--transition-fast);
  background-color: var(--color-bg);
  color: var(--color-text);
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 图片响应式 */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* 列表样式 */
ul, ol {
  margin: 0 0 var(--spacing-md) 0;
  padding-left: var(--spacing-lg);
}

li {
  margin-bottom: var(--spacing-xs);
}

/* 容器类 */
.container {
  width: 100%;
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.container-sm { max-width: var(--container-sm); }
.container-md { max-width: var(--container-md); }
.container-lg { max-width: var(--container-lg); }
.container-2xl { max-width: var(--container-2xl); }

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none; }
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 响应式断点 */
@media (max-width: 640px) {
  html { font-size: 56.25%; } /* 9px */
}

@media (max-width: 480px) {
  html { font-size: 50%; } /* 8px */
}

@media (min-width: 1200px) {
  html { font-size: 68.75%; } /* 11px */
}

@media (min-width: 1600px) {
  html { font-size: 75%; } /* 12px */
}

/* 减少动画效果（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
