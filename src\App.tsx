import { useState, useMemo } from 'react'
import CardCarousel from './components/CardCarousel'
import InfiniteScroll from './components/InfiniteScroll'
import './App.css'

// 卡片数据
const carouselCards = [
  {
    id: 1,
    title: "React Hooks 深度解析",
    content: "探索 useState, useEffect, useCallback, useMemo 等核心 hooks 的使用技巧和最佳实践。",
    image: "https://picsum.photos/400/300?random=1",
    color: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
  },
  {
    id: 2,
    title: "响应式设计实战",
    content: "使用 CSS Grid, Flexbox 和现代 CSS 单位创建适配所有设备的响应式布局。",
    image: "https://picsum.photos/400/300?random=2",
    color: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)"
  },
  {
    id: 3,
    title: "性能优化技巧",
    content: "图片懒加载、虚拟滚动、代码分割等前端性能优化的实用技术。",
    image: "https://picsum.photos/400/300?random=3",
    color: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)"
  },
  {
    id: 4,
    title: "TypeScript 最佳实践",
    content: "在 React 项目中使用 TypeScript 的类型定义、泛型和高级特性。",
    image: "https://picsum.photos/400/300?random=4",
    color: "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)"
  },
  {
    id: 5,
    title: "现代 CSS 技术",
    content: "CSS 变量、Grid 布局、Flexbox 和动画效果的现代化应用。",
    image: "https://picsum.photos/400/300?random=5",
    color: "linear-gradient(135deg, #fa709a 0%, #fee140 100%)"
  }
];

function App() {
  const [activeDemo, setActiveDemo] = useState<'carousel' | 'scroll'>('carousel');



  return (
    <div className="app">
      <div className="app-container">
        {/* 头部介绍 */}
          <h1 className="app-title">测试演示</h1>

        {/* 演示切换 */}
        <div className="demo-section">
          <div style={{ textAlign: 'center', marginBottom: 'var(--spacing-lg)' }}>
            <button
              onClick={() => setActiveDemo('carousel')}
              style={{
                marginRight: 'var(--spacing-sm)',
                backgroundColor: activeDemo === 'carousel' ? 'var(--color-primary)' : 'var(--color-gray-400)'
              }}
            >
              卡片轮播演示
            </button>
            <button
              onClick={() => setActiveDemo('scroll')}
              style={{
                backgroundColor: activeDemo === 'scroll' ? 'var(--color-primary)' : 'var(--color-gray-400)'
              }}
            >
              无限滚动演示
            </button>
          </div>

          {/* 卡片轮播演示 */}
          {activeDemo === 'carousel' && (
            <div>
              <h2 className="demo-title">🎠 卡片轮播组件</h2>
              <CardCarousel
                cards={carouselCards}
                autoPlayInterval={4000}
                pauseOnHover={true}
              />
              <div style={{
                textAlign: 'center',
                marginTop: 'var(--spacing-lg)',
                padding: 'var(--spacing-md)',
                backgroundColor: 'var(--color-gray-50)',
                borderRadius: 'var(--radius-md)',
                fontSize: 'var(--font-size-sm)',
                color: 'var(--color-text-secondary)'
              }}>
                <p><strong>功能特点：</strong></p>
                <p>• 无限循环轮播，到达最后一张自动回到第一张</p>
                <p>• 鼠标悬停暂停，移开后自动恢复播放</p>
                <p>• 支持手动控制：上一张、下一张、播放/暂停</p>
                <p>• 响应式设计，适配各种屏幕尺寸</p>
                <p>• 使用了 useState、useEffect、useCallback、useMemo、useRef、useLayoutEffect 等多个 React hooks</p>
              </div>
            </div>
          )}

          {/* 无限滚动演示 */}
          {activeDemo === 'scroll' && (
            <div>
              <h2 className="demo-title">📜 无限滚动 + 图片懒加载</h2>
              <InfiniteScroll
                itemsPerPage={6}
              />
              <div style={{
                textAlign: 'center',
                marginTop: 'var(--spacing-lg)',
                padding: 'var(--spacing-md)',
                backgroundColor: 'var(--color-gray-50)',
                borderRadius: 'var(--radius-md)',
                fontSize: 'var(--font-size-sm)',
                color: 'var(--color-text-secondary)'
              }}>
                <p><strong>功能特点：</strong></p>
                <p>• 滚动到底部自动加载更多内容</p>
                <p>• 图片懒加载，只有进入视口才开始加载</p>
                <p>• 加载状态指示和错误处理</p>
                <p>• 响应式网格布局</p>
                <p>• 使用了自定义 hooks：useIntersectionObserver、useInfiniteScroll</p>
              </div>
            </div>
          )}
        </div>

        {/* 技术说明 */}
        <footer style={{
          textAlign: 'center',
          color: 'white',
          opacity: 0.8,
          fontSize: 'var(--font-size-sm)'
        }}>
          <p>本项目展示了现代 React 开发的最佳实践</p>
          <p>包含响应式设计、性能优化、自定义组件和 Hooks 等核心技术</p>
        </footer>
      </div>
    </div>
  )
}

export default App
