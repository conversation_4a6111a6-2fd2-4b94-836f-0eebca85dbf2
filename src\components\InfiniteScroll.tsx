import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { useInfiniteScroll } from '../hooks/useInfiniteScroll';
import LazyImage from './LazyImage';
import './InfiniteScroll.css';

interface InfiniteScrollItem {
  id: number;
  title: string;
  description: string;
  image: string;
  category: string;
  date: string;
}

interface InfiniteScrollProps {
  initialItems?: InfiniteScrollItem[];
  itemsPerPage?: number;
  loadingComponent?: React.ReactNode;
  emptyComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
  className?: string;
}

// 模拟数据生成器
const generateMockItems = (startId: number, count: number): InfiniteScrollItem[] => {
  const categories = ['Technology', 'Design', 'Photography', 'Travel', 'Food', 'Art'];
  const items: InfiniteScrollItem[] = [];
  
  for (let i = 0; i < count; i++) {
    const id = startId + i;
    items.push({
      id,
      title: `Item ${id}: Lorem Ipsum Title`,
      description: `This is a detailed description for item ${id}. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.`,
      image: `https://picsum.photos/400/300?random=${id}`,
      category: categories[Math.floor(Math.random() * categories.length)],
      date: new Date(Date.now() - Math.random() * 10000000000).toLocaleDateString()
    });
  }
  
  return items;
};

const InfiniteScroll: React.FC<InfiniteScrollProps> = ({
  initialItems = [],
  itemsPerPage = 10,
  loadingComponent,
  emptyComponent,
  errorComponent,
  className = ''
}) => {
  const [items, setItems] = useState<InfiniteScrollItem[]>(() => 
    initialItems.length > 0 ? initialItems : generateMockItems(1, itemsPerPage)
  );
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 模拟加载更多数据的函数
  const fetchMoreItems = useCallback(async () => {
    if (loading || !hasMore) return;

    setLoading(true);
    setError(null);

    try {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000));
      
      // 模拟偶尔的错误
      if (Math.random() < 0.1) {
        throw new Error('Failed to load more items');
      }

      const newItems = generateMockItems(items.length + 1, itemsPerPage);
      
      setItems(prevItems => [...prevItems, ...newItems]);
      
      // 模拟数据有限的情况
      if (items.length + newItems.length >= 100) {
        setHasMore(false);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, [items.length, itemsPerPage, loading, hasMore]);

  // 使用自定义的无限滚动hook
  const { lastElementRef } = useInfiniteScroll(fetchMoreItems, {
    hasMore,
    loading,
    threshold: 1.0,
    rootMargin: '100px'
  });

  // 重试函数
  const handleRetry = useCallback(() => {
    setError(null);
    fetchMoreItems();
  }, [fetchMoreItems]);

  // 重置函数
  const handleReset = useCallback(() => {
    setItems(generateMockItems(1, itemsPerPage));
    setHasMore(true);
    setError(null);
    setLoading(false);
  }, [itemsPerPage]);

  // 默认组件
  const defaultLoadingComponent = useMemo(() => (
    <div className="infinite-scroll-loading">
      <div className="loading-spinner"></div>
      <p>Loading more items...</p>
    </div>
  ), []);

  const defaultEmptyComponent = useMemo(() => (
    <div className="infinite-scroll-empty">
      <p>No items to display</p>
      <button onClick={handleReset} className="reset-button">
        Load Items
      </button>
    </div>
  ), [handleReset]);

  const defaultErrorComponent = useMemo(() => (
    <div className="infinite-scroll-error">
      <p>Error: {error}</p>
      <button onClick={handleRetry} className="retry-button">
        Retry
      </button>
    </div>
  ), [error, handleRetry]);

  if (items.length === 0 && !loading) {
    return (
      <div className={`infinite-scroll ${className}`}>
        {emptyComponent || defaultEmptyComponent}
      </div>
    );
  }

  return (
    <div className={`infinite-scroll ${className}`}>
      <div className="infinite-scroll-header">
        <h2>Infinite Scroll Demo</h2>
        <div className="scroll-stats">
          <span>Items: {items.length}</span>
          <button onClick={handleReset} className="reset-button">
            Reset
          </button>
        </div>
      </div>

      <div className="infinite-scroll-grid">
        {items.map((item, index) => (
          <div
            key={item.id}
            className="infinite-scroll-item"
            ref={index === items.length - 1 ? lastElementRef : null}
          >
            <div className="item-image">
              <LazyImage
                src={item.image}
                alt={item.title}
                width="100%"
                height="200px"
              />
            </div>
            <div className="item-content">
              <div className="item-meta">
                <span className="item-category">{item.category}</span>
                <span className="item-date">{item.date}</span>
              </div>
              <h3 className="item-title">{item.title}</h3>
              <p className="item-description">{item.description}</p>
            </div>
          </div>
        ))}
      </div>

      {/* 加载状态 */}
      {loading && (loadingComponent || defaultLoadingComponent)}

      {/* 错误状态 */}
      {error && (errorComponent || defaultErrorComponent)}

      {/* 没有更多数据 */}
      {!hasMore && !loading && (
        <div className="infinite-scroll-end">
          <p>🎉 You've reached the end!</p>
          <p>Total items loaded: {items.length}</p>
        </div>
      )}
    </div>
  );
};

export default InfiniteScroll;
