import { useEffect, useRef, useCallback, useState } from 'react';

interface UseAutoPlayOptions {
  interval?: number;
  autoStart?: boolean;
  pauseOnHover?: boolean;
}

interface UseAutoPlayReturn {
  isPlaying: boolean;
  isPaused: boolean;
  start: () => void;
  stop: () => void;
  toggle: () => void;
  pause: () => void;
  resume: () => void;
}

/**
 * 自定义hook：管理自动播放功能
 * 用于轮播组件的自动播放控制
 */
export const useAutoPlay = (
  callback: () => void,
  options: UseAutoPlayOptions = {}
): UseAutoPlayReturn => {
  const {
    interval = 3000,
    autoStart = true,
    pauseOnHover = true
  } = options;

  const [isPlaying, setIsPlaying] = useState(autoStart);
  const [isPaused, setIsPaused] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const callbackRef = useRef(callback);

  // 更新回调函数引用
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  const clearTimer = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  const startTimer = useCallback(() => {
    clearTimer();
    intervalRef.current = setInterval(() => {
      callbackRef.current();
    }, interval);
  }, [interval, clearTimer]);

  const start = useCallback(() => {
    setIsPlaying(true);
    if (!isPaused) {
      startTimer();
    }
  }, [isPaused, startTimer]);

  const stop = useCallback(() => {
    setIsPlaying(false);
    clearTimer();
  }, [clearTimer]);

  const toggle = useCallback(() => {
    if (isPlaying) {
      stop();
    } else {
      start();
    }
  }, [isPlaying, start, stop]);

  const pause = useCallback(() => {
    if (pauseOnHover) {
      setIsPaused(true);
      clearTimer();
    }
  }, [pauseOnHover, clearTimer]);

  const resume = useCallback(() => {
    if (pauseOnHover) {
      setIsPaused(false);
      if (isPlaying) {
        startTimer();
      }
    }
  }, [pauseOnHover, isPlaying, startTimer]);

  // 管理定时器
  useEffect(() => {
    if (isPlaying && !isPaused) {
      startTimer();
    } else {
      clearTimer();
    }

    return clearTimer;
  }, [isPlaying, isPaused, startTimer, clearTimer]);

  // 清理定时器
  useEffect(() => {
    return () => {
      clearTimer();
    };
  }, [clearTimer]);

  return {
    isPlaying,
    isPaused,
    start,
    stop,
    toggle,
    pause,
    resume
  };
};
