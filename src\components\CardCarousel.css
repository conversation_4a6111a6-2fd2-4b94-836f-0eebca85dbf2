/* 使用 CSS 变量和响应式单位 */
:root {
  --carousel-card-width: min(90vw, 400px);
  --carousel-card-height: min(60vh, 300px);
  --carousel-gap: 1rem;
  --carousel-border-radius: 1rem;
  --carousel-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.1);
  --carousel-transition: all 0.3s ease;
}

.card-carousel {
  width: 100%;
  max-width: 100vw;
  margin: 2rem auto;
  padding: 0 1rem;
  box-sizing: border-box;
}

.carousel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 0 1rem;
}

.carousel-header h2 {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  color: #333;
  margin: 0;
  font-weight: 600;
}

.carousel-controls {
  display: flex;
  gap: 0.5rem;
}

.control-btn {
  width: 3rem;
  height: 3rem;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  transition: var(--carousel-transition);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--carousel-shadow);
}

.control-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.75rem 2.5rem rgba(0, 0, 0, 0.15);
}

.control-btn:active {
  transform: translateY(0);
}

.carousel-container {
  position: relative;
  width: 100%;
  height: var(--carousel-card-height);
  overflow: hidden;
  border-radius: var(--carousel-border-radius);
  box-shadow: var(--carousel-shadow);
  background: #f8f9fa;
}

.carousel-track {
  display: flex;
  height: 100%;
  will-change: transform;
}

.carousel-card {
  flex: 0 0 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-image {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--carousel-transition);
}

.carousel-card:hover .card-image img {
  transform: scale(1.05);
}

.card-content {
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  transform: translateY(60%);
  transition: var(--carousel-transition);
}

.carousel-card:hover .card-content {
  transform: translateY(0);
}

.card-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: clamp(1.2rem, 3vw, 1.5rem);
  color: #333;
  font-weight: 600;
}

.card-content p {
  margin: 0;
  font-size: clamp(0.9rem, 2.5vw, 1rem);
  color: #666;
  line-height: 1.5;
}

.carousel-indicators {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1.5rem;
  padding: 0 1rem;
}

.indicator {
  width: 0.75rem;
  height: 0.75rem;
  border: none;
  border-radius: 50%;
  background: #ddd;
  cursor: pointer;
  transition: var(--carousel-transition);
}

.indicator.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scale(1.2);
}

.indicator:hover {
  background: #bbb;
}

.indicator.active:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.carousel-status {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.status-indicator {
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: var(--carousel-transition);
}

.status-indicator.playing {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.status-indicator.paused {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
}

.carousel-empty {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
  font-size: 1.2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  :root {
    --carousel-card-width: 95vw;
    --carousel-card-height: 50vh;
  }
  
  .carousel-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .control-btn {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1rem;
  }
  
  .card-content {
    padding: 1rem;
    transform: translateY(40%);
  }
}

@media (max-width: 480px) {
  :root {
    --carousel-card-height: 40vh;
  }
  
  .card-carousel {
    margin: 1rem auto;
    padding: 0 0.5rem;
  }
  
  .carousel-header {
    margin-bottom: 1rem;
  }
  
  .card-content {
    padding: 0.75rem;
    transform: translateY(30%);
  }
  
  .carousel-indicators {
    margin-top: 1rem;
  }
}

/* 高分辨率屏幕优化 */
@media (min-width: 1200px) {
  :root {
    --carousel-card-width: min(80vw, 500px);
    --carousel-card-height: min(50vh, 350px);
  }
}

/* 减少动画效果（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .carousel-track,
  .card-image img,
  .card-content,
  .control-btn,
  .indicator,
  .status-indicator {
    transition: none;
  }
}
