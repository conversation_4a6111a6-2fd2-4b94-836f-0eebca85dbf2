import { useEffect, useCallback, useState, useRef } from 'react';

interface UseInfiniteScrollOptions {
  threshold?: number;
  hasMore?: boolean;
  loading?: boolean;
  rootMargin?: string;
}

interface UseInfiniteScrollReturn {
  isFetching: boolean;
  setIsFetching: (fetching: boolean) => void;
  lastElementRef: (node: HTMLElement | null) => void;
}

/**
 * 自定义hook：实现无限滚动功能
 * 当滚动到底部时自动触发加载更多数据的回调
 */
export const useInfiniteScroll = (
  fetchMore: () => Promise<void> | void,
  options: UseInfiniteScrollOptions = {}
): UseInfiniteScrollReturn => {
  const {
    threshold = 1.0,
    hasMore = true,
    loading = false,
    rootMargin = '100px'
  } = options;

  const [isFetching, setIsFetching] = useState(false);
  const observer = useRef<IntersectionObserver | null>(null);

  const lastElementRef = useCallback(
    (node: HTMLElement | null) => {
      if (loading || isFetching) return;
      
      if (observer.current) {
        observer.current.disconnect();
      }

      observer.current = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting && hasMore && !loading && !isFetching) {
            setIsFetching(true);
            
            const result = fetchMore();
            
            if (result instanceof Promise) {
              result
                .then(() => setIsFetching(false))
                .catch(() => setIsFetching(false));
            } else {
              // 如果不是Promise，延迟一下再设置为false，避免过快触发
              setTimeout(() => setIsFetching(false), 100);
            }
          }
        },
        {
          threshold,
          rootMargin
        }
      );

      if (node) {
        observer.current.observe(node);
      }
    },
    [loading, isFetching, hasMore, fetchMore, threshold, rootMargin]
  );

  useEffect(() => {
    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, []);

  return {
    isFetching,
    setIsFetching,
    lastElementRef
  };
};
